# 支付宝网页支付完整实现方案：包含异步通知、错误处理与日志记录

## 一、支付宝平台配置

1. **创建应用**：在支付宝开放平台创建应用，获取APP_ID
2. **配置密钥**：生成RSA2密钥对，上传公钥至支付宝平台 
3. **配置回调地址**：设置异步通知URL(notify_url)和同步返回URL(return_url) 
4. **启用HTTPS**：确保服务器支持HTTPS，支付宝要求异步通知必须通过HTTPS传输 

## 二、后端完整实现（Node.js + Express）

### 1. 项目结构与依赖

```bash
project-root/
├── config/
│   └── alipay.js       # 支付宝配置
├── controllers/
│   ├── payment.js      # 支付逻辑
│   └── notification.js # 通知处理
├── services/
│   ├── alipayService.js # 支付宝服务封装
│   └── logger.js       # 日志服务
├── models/
│   └── order.js        # 订单模型
└── app.js              # 主应用
```

### 2. 安全配置（config/alipay.js）

```javascript
require('dotenv').config();

module.exports = {
  appId: process.env.ALIPAY_APP_ID,
  privateKey: process.env.ALIPAY_PRIVATE_KEY,
  publicKey: process.env.ALIPAY_PUBLIC_KEY, // 支付宝公钥
  notifyUrl: process.env.ALIPAY_NOTIFY_URL,
  returnUrl: process.env.ALIPAY_RETURN_URL,
  gateway: 'https://openapi.alipay.com/gateway.do', // 正式环境
  // gateway: 'https://openapi.alipaydev.com/gateway.do' // 沙箱环境
};
```

### 3. 日志服务（services/logger.js）

```javascript
const fs = require('fs');
const path = require('path');
const winston = require('winston');

// 确保日志目录存在
const logDir = 'logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// 创建自定义日志格式
const formatter = winston.format.printf(({ level, message, timestamp, ...metadata }) => {
  let msg = `${timestamp} [${level.toUpperCase()}] ${message}`;
  
  if (metadata && Object.keys(metadata).length > 0) {
    msg += ` ${JSON.stringify(metadata)}`;
  }
  
  return msg;
});

// 创建支付宝专用日志记录器
const alipayLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    formatter
  ),
  transports: [
    // 写入所有日志到支付宝专用文件
    new winston.transports.File({ 
      filename: path.join(logDir, 'alipay.log'),
      level: 'info'
    }),
    // 错误日志单独记录
    new winston.transports.File({ 
      filename: path.join(logDir, 'alipay-error.log'),
      level: 'error'
    })
  ]
});

// 记录支付关键日志，包括订单号、支付类型和交易信息 
const logPaymentEvent = (eventType, orderId, amount, additionalData = {}) => {
  alipayLogger.info(`Payment Event: ${eventType}`, {
    orderId,
    amount,
    ...additionalData,
    timestamp: new Date().toISOString()
  });
};

// 记录支付宝API调用日志
const logAlipayApiCall = (apiName, request, response) => {
  alipayLogger.info(`Alipay API Call: ${apiName}`, {
    request,
    response
  });
};

// 记录SQL日志（在实际项目中与数据库操作集成）
const logSqlQuery = (query, params) => {
  alipayLogger.debug('SQL Query', { query, params });
};

module.exports = {
  alipayLogger,
  logPaymentEvent,
  logAlipayApiCall,
  logSqlQuery
};
```

### 4. 支付宝服务封装（services/alipayService.js）

```javascript
const AlipaySdk = require('alipay-sdk').default;
const AlipayFormData = require('alipay-sdk/lib/form').default;
const crypto = require('crypto');
const { alipayLogger, logAlipayApiCall, logPaymentEvent } = require('./logger');
const alipayConfig = require('../config/alipay');

// 创建支付宝SDK实例
const alipaySdk = new AlipaySdk({
  appId: alipayConfig.appId,
  privateKey: alipayConfig.privateKey,
  signType: 'RSA2',
  gateway: alipayConfig.gateway
});

// 验证支付宝通知签名
const verifySign = (params, sign) => {
  try {
    // 移除sign参数
    const { sign: _, sign_type: __, ...paramsWithoutSign } = params;
    
    // 将参数按字母排序
    const sortedParams = Object.keys(paramsWithoutSign)
      .sort()
      .reduce((acc, key) => {
        acc[key] = paramsWithoutSign[key];
        return acc;
      }, {});
    
    // 构建待签名字符串
    const stringToSign = Object.keys(sortedParams)
      .map(key => `${key}=${sortedParams[key]}`)
      .join('&');
    
    // 使用支付宝公钥验证签名
    const verifier = crypto.createVerify('RSA-SHA256');
    verifier.update(stringToSign);
    return verifier.verify(alipayConfig.publicKey, sign, 'base64');
  } catch (error) {
    alipayLogger.error('签名验证失败', { error: error.message, params });
    return false;
  }
};

// 处理支付宝异步通知 
const handleNotification = async (params) => {
  try {
    // 1. 验证签名
    if (!verifySign(params, params.sign)) {
      alipayLogger.error('异步通知签名验证失败', { params });
      return { success: false, message: '签名验证失败' };
    }
    
    // 2. 检查交易状态
    if (params.trade_status !== 'TRADE_SUCCESS' && params.trade_status !== 'TRADE_FINISHED') {
      alipayLogger.info('非成功交易状态', { tradeStatus: params.trade_status, params });
      return { success: true }; // 返回成功，避免支付宝重复通知
    }
    
    // 3. 检查订单是否已处理（防止重复通知）
    const isOrderProcessed = await checkIfOrderProcessed(params.out_trade_no);
    if (isOrderProcessed) {
      alipayLogger.info('订单已处理，忽略重复通知', { orderId: params.out_trade_no });
      return { success: true };
    }
    
    // 4. 验证金额
    const order = await getOrderById(params.out_trade_no);
    if (!order || order.amount !== parseFloat(params.total_amount)) {
      alipayLogger.error('订单金额不匹配', { 
        orderId: params.out_trade_no, 
        expected: order ? order.amount : '不存在', 
        actual: params.total_amount 
      });
      return { success: false, message: '金额不匹配' };
    }
    
    // 5. 更新订单状态
    await updateOrderStatus(params.out_trade_no, 'PAID', {
      transactionId: params.trade_no,
      paymentTime: new Date(params.gmt_payment)
    });
    
    // 6. 记录支付成功日志 
    logPaymentEvent('PAYMENT_SUCCESS', params.out_trade_no, params.total_amount, {
      tradeNo: params.trade_no,
      buyerId: params.buyer_id
    });
    
    return { success: true };
  } catch (error) {
    alipayLogger.error('处理异步通知时出错', { 
      error: error.message, 
      stack: error.stack,
      params 
    });
    return { success: false, message: '处理失败' };
  }
};

// 检查订单是否已处理（实际项目中应查询数据库）
const checkIfOrderProcessed = async (orderId) => {
  // 实际项目中这里应查询数据库
  // return db.order.findFirst({ where: { id: orderId, status: 'PAID' } });
  return false; // 简化示例
};

// 获取订单信息（实际项目中应查询数据库）
const getOrderById = async (orderId) => {
  // 实际项目中这里应查询数据库
  // return db.order.findUnique({ where: { id: orderId } });
  return { id: orderId, amount: 10.00, status: 'PENDING' }; // 简化示例
};

// 更新订单状态（实际项目中应更新数据库）
const updateOrderStatus = async (orderId, status, details) => {
  // 实际项目中这里应更新数据库
  // return db.order.update({
  //   where: { id: orderId },
  //    { status, ...details }
  // });
  alipayLogger.info('更新订单状态', { orderId, status, details });
};

// 创建支付订单
const createPaymentOrder = async (orderId, amount, subject, storeId = 'web') => {
  try {
    const result = await alipaySdk.exec('alipay.trade.precreate', {
      bizContent: {
        out_trade_no: orderId,
        total_amount: amount,
        subject: subject,
        store_id: storeId,
        notify_url: alipayConfig.notifyUrl,
        return_url: alipayConfig.returnUrl
      }
    });
    
    // 记录API调用日志
    logAlipayApiCall('alipay.trade.precreate', { orderId, amount }, result);
    
    if (result.code === '10000') {
      // 记录支付日志，包括订单号、支付类型和交易 
      logPaymentEvent('ORDER_CREATED', orderId, amount, {
        qrCode: result.qr_code,
        subject
      });
      return { success: true, qrCode: result.qr_code };
    } else {
      // 记录支付系统错误日志 
      logPaymentEvent('ORDER_CREATION_FAILED', orderId, amount, {
        errorCode: result.code,
        errorMsg: result.msg,
        subCode: result.sub_code,
        subMsg: result.sub_msg
      });
      
      // 对于超时或未知异常，需要记录并考虑重试机制 
      if (result.code === '20000') { // 服务不可用
        alipayLogger.warn('支付宝服务暂时不可用，建议重试', { orderId, result });
      }
      
      return { 
        success: false, 
        message: result.sub_msg || result.msg,
        code: result.code
      };
    }
  } catch (error) {
    alipayLogger.error('创建支付订单时出错', { 
      error: error.message, 
      stack: error.stack,
      orderId,
      amount
    });
    
    // 记录异常授权操作，便于后续排查 
    logPaymentEvent('EXCEPTION', orderId, amount, {
      errorType: 'API_CALL_ERROR',
      errorMessage: error.message
    });
    
    return { 
      success: false, 
      message: '支付服务暂时不可用，请稍后重试',
      code: 'SERVER_ERROR'
    };
  }
};

// 查询订单状态
const queryOrderStatus = async (orderId) => {
  try {
    const result = await alipaySdk.exec('alipay.trade.query', {
      bizContent: {
        out_trade_no: orderId
      }
    });
    
    logAlipayApiCall('alipay.trade.query', { orderId }, result);
    
    if (result.code === '10000') {
      if (result.trade_status === 'TRADE_SUCCESS' || result.trade_status === 'TRADE_FINISHED') {
        return { paid: true, tradeNo: result.trade_no };
      }
      return { paid: false };
    }
    
    // 记录查询错误
    logPaymentEvent('ORDER_QUERY_FAILED', orderId, null, {
      errorCode: result.code,
      errorMsg: result.msg
    });
    
    return { paid: false, error: result.sub_msg || result.msg };
  } catch (error) {
    alipayLogger.error('查询订单状态时出错', { 
      error: error.message, 
      stack: error.stack,
      orderId
    });
    
    return { paid: false, error: '查询服务暂时不可用' };
  }
};

module.exports = {
  alipaySdk,
  createPaymentOrder,
  queryOrderStatus,
  handleNotification,
  verifySign
};
```

### 5. 支付控制器（controllers/payment.js）

```javascript
const { createPaymentOrder, queryOrderStatus } = require('../services/alipayService');
const { alipayLogger, logPaymentEvent } = require('../services/logger');

// 创建支付订单
exports.createOrder = async (req, res) => {
  const { orderId, amount, subject } = req.body;
  
  // 参数验证
  if (!orderId || !amount || !subject) {
    alipayLogger.warn('创建订单参数缺失', { body: req.body });
    return res.status(400).json({ 
      success: false, 
      message: '缺少必要参数: orderId, amount, subject' 
    });
  }
  
  // 金额验证
  const amountNum = parseFloat(amount);
  if (isNaN(amountNum) || amountNum <= 0) {
    alipayLogger.warn('无效的金额', { amount });
    return res.status(400).json({ 
      success: false, 
      message: '无效的金额' 
    });
  }
  
  try {
    const result = await createPaymentOrder(orderId, amount, subject);
    
    if (result.success) {
      res.json(result);
    } else {
      // 记录业务层错误
      logPaymentEvent('CREATE_ORDER_ERROR', orderId, amount, {
        errorCode: result.code,
        errorMessage: result.message
      });
      
      res.status(400).json(result);
    }
  } catch (error) {
    alipayLogger.error('创建订单内部错误', { 
      error: error.message, 
      stack: error.stack,
      body: req.body
    });
    
    res.status(500).json({ 
      success: false, 
      message: '服务器内部错误' 
    });
  }
};

// 查询订单状态
exports.checkPayment = async (req, res) => {
  const { orderId } = req.query;
  
  if (!orderId) {
    return res.status(400).json({ 
      success: false, 
      message: '缺少orderId参数' 
    });
  }
  
  try {
    const result = await queryOrderStatus(orderId);
    
    if (result.error) {
      return res.json({ 
        paid: false,
        error: result.error
      });
    }
    
    res.json({ paid: result.paid, tradeNo: result.tradeNo });
  } catch (error) {
    alipayLogger.error('查询支付状态错误', { 
      error: error.message, 
      stack: error.stack,
      orderId
    });
    
    res.json({ paid: false, error: '查询服务暂时不可用' });
  }
};
```

### 6. 通知控制器（controllers/notification.js）

```javascript
const { handleNotification } = require('../services/alipayService');
const { alipayLogger } = require('../services/logger');

// 处理支付宝异步通知 
exports.handleAlipayNotify = async (req, res) => {
  try {
    alipayLogger.info('收到支付宝异步通知', { body: req.body });
    
    // 处理通知
    const result = await handleNotification(req.body);
    
    if (result.success) {
      // 必须返回success，否则支付宝会重复通知
      res.send('success');
    } else {
      alipayLogger.error('处理异步通知失败', { 
        message: result.message,
        body: req.body 
      });
      
      // 返回错误，让支付宝重试
      res.status(500).send('fail');
    }
  } catch (error) {
    alipayLogger.error('处理异步通知时发生未捕获异常', { 
      error: error.message, 
      stack: error.stack,
      body: req.body 
    });
    
    // 返回错误，让支付宝重试
    res.status(500).send('fail');
  }
};
```

### 7. 主应用配置（app.js）

```javascript
const express = require('express');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const { alipayLogger } = require('./services/logger');
const paymentController = require('./controllers/payment');
const notificationController = require('./controllers/notification');

dotenv.config();

const app = express();

// 中间件
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 日志中间件
app.use((req, res, next) => {
  const start = Date.now();
  alipayLogger.info('Request received', { 
    method: req.method, 
    url: req.url,
    ip: req.ip,
    headers: {
      'user-agent': req.get('User-Agent'),
      'accept': req.get('Accept')
    }
  });
  
  const originalSend = res.send;
  res.send = function(body) {
    const duration = Date.now() - start;
    alipayLogger.info('Response sent', { 
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`
    });
    return originalSend.apply(res, [body]);
  };
  
  next();
});

// 路由
app.post('/api/create-order', paymentController.createOrder);
app.get('/api/check-payment', paymentController.checkPayment);
app.post('/api/alipay-notify', notificationController.handleAlipayNotify); // 支付宝异步通知URL

// 错误处理中间件
app.use((err, req, res, next) => {
  alipayLogger.error('全局错误处理', { 
    error: err.message, 
    stack: err.stack,
    url: req.url,
    method: req.method
  });
  
  res.status(500).json({ 
    success: false, 
    message: '服务器内部错误' 
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  alipayLogger.info(`服务器运行在端口 ${PORT}`);
  console.log(`服务器运行在端口 ${PORT}`);
});

// 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  alipayLogger.error('未处理的Promise拒绝', { 
    reason: reason ? reason.message : '未知原因',
    stack: reason && reason.stack
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  alipayLogger.error('未捕获的异常', { 
    error: error.message, 
    stack: error.stack
  });
  
  // 在记录错误后安全退出
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});
```

## 三、前端实现（完整版）

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付宝支付</title>
  <script src="https://cdn.jsdelivr.net/npm/qrcode.js@1.0.0/qrcode.min.js"></script>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
      padding: 20px;
    }
    
    .container {
      max-width: 500px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: #1677ff;
      color: white;
      padding: 15px 20px;
      text-align: center;
    }
    
    .content {
      padding: 25px;
    }
    
    .amount {
      font-size: 28px;
      color: #f60;
      font-weight: bold;
      margin: 15px 0;
      text-align: center;
    }
    
    .qrcode-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 4px;
    }
    
    .instructions {
      margin: 20px 0;
      padding: 15px;
      background: #f0f7ff;
      border-left: 4px solid #1677ff;
      border-radius: 0 4px 4px 0;
    }
    
    .instructions h3 {
      margin-bottom: 10px;
      color: #1677ff;
    }
    
    .instructions ul {
      padding-left: 20px;
    }
    
    .instructions li {
      margin-bottom: 8px;
    }
    
    .status {
      text-align: center;
      padding: 15px;
      font-weight: bold;
      border-radius: 4px;
    }
    
    .status.waiting {
      background: #fffbe6;
      color: #faad14;
    }
    
    .status.success {
      background: #f6ffed;
      color: #52c41a;
    }
    
    .status.error {
      background: #fff2f4;
      color: #f5222d;
    }
    
    .retry-btn {
      display: inline-block;
      margin-top: 15px;
      padding: 8px 16px;
      background: #1677ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .retry-btn:hover {
      background: #4096ff;
    }
    
    .loading {
      text-align: center;
      padding: 10px;
    }
    
    .spinner {
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 3px solid #1677ff;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-details {
      margin-top: 10px;
      font-size: 12px;
      color: #8c8c8c;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>支付宝支付</h1>
    </div>
    <div class="content">
      <p>请使用支付宝扫描下方二维码完成支付</p>
      <div class="amount">¥<span id="amount">0.00</span></div>
      
      <div id="qrcode-container" class="qrcode-container">
        <div id="qrcode"></div>
      </div>
      
      <div class="instructions">
        <h3>支付说明</h3>
        <ul>
          <li>请确认支付金额与订单一致</li>
          <li>支付完成后，系统将自动跳转</li>
          <li>如遇问题，请联系客服</li>
        </ul>
      </div>
      
      <div id="status" class="status waiting">
        <div class="loading">
          <div class="spinner"></div>
          等待支付...
        </div>
      </div>
      
      <div id="error-details" class="error-details" style="display: none;"></div>
    </div>
  </div>

  <script>
    // 支付配置
    const PAYMENT_CONFIG = {
      MAX_ATTEMPTS: 15,     // 最大尝试次数
      POLL_INTERVAL: 3000,  // 轮询间隔(毫秒)
      CREATE_ORDER_TIMEOUT: 30000, // 创建订单超时时间
      CHECK_STATUS_TIMEOUT: 10000  // 检查状态超时时间
    };
    
    // 支付状态
    const PAYMENT_STATUS = {
      PENDING: 'pending',
      SUCCESS: 'success',
      FAILED: 'failed',
      EXPIRED: 'expired',
      ERROR: 'error'
    };
    
    // 支付服务类
    class PaymentService {
      constructor() {
        this.orderId = null;
        this.amount = null;
        this.subject = null;
        this.paymentStatus = PAYMENT_STATUS.PENDING;
        this.attempts = 0;
        this.pollingInterval = null;
        this.createOrderTimeout = null;
        this.checkStatusTimeout = null;
        
        // DOM元素引用
        this.amountEl = document.getElementById('amount');
        this.qrcodeEl = document.getElementById('qrcode');
        this.statusEl = document.getElementById('status');
        this.errorDetailsEl = document.getElementById('error-details');
      }
      
      // 初始化支付
      async init() {
        try {
          // 生成订单信息
          this.orderId = 'ORDER_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
          this.amount = '10.00';
          this.subject = '测试商品支付';
          
          // 显示金额
          this.amountEl.textContent = this.amount;
          
          // 创建订单
          await this.createOrder();
          
          // 开始轮询检查支付状态
          this.startPolling();
          
        } catch (error) {
          this.handleError(error, '初始化支付失败');
        }
      }
      
      // 创建支付订单
      async createOrder() {
        this.setStatus(PAYMENT_STATUS.PENDING, '正在生成支付二维码...');
        
        return new Promise((resolve, reject) => {
          // 设置创建订单超时
          this.createOrderTimeout = setTimeout(() => {
            reject(new Error('创建订单超时'));
          }, PAYMENT_CONFIG.CREATE_ORDER_TIMEOUT);
          
          fetch('/api/create-order', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              orderId: this.orderId,
              amount: this.amount,
              subject: this.subject
            })
          })
          .then(response => {
            clearTimeout(this.createOrderTimeout);
            
            if (!response.ok) {
              return response.json().then(err => {
                throw new Error(err.message || '创建订单失败');
              });
            }
            return response.json();
          })
          .then(data => {
            if (data.success) {
              // 生成二维码
              new QRCode(this.qrcodeEl, {
                text: data.qrCode,
                width: 200,
                height: 200,
                correctLevel: QRCode.CorrectLevel.H
              });
              
              this.logEvent('ORDER_CREATED', {
                orderId: this.orderId,
                amount: this.amount
              });
              
              resolve();
            } else {
              throw new Error(data.message || '创建订单失败');
            }
          })
          .catch(error => {
            clearTimeout(this.createOrderTimeout);
            reject(error);
          });
        });
      }
      
      // 开始轮询检查支付状态
      startPolling() {
        this.pollingInterval = setInterval(() => {
          this.checkPaymentStatus();
        }, PAYMENT_CONFIG.POLL_INTERVAL);
      }
      
      // 停止轮询
      stopPolling() {
        if (this.pollingInterval) {
          clearInterval(this.pollingInterval);
          this.pollingInterval = null;
        }
      }
      
      // 检查支付状态
      async checkPaymentStatus() {
        if (this.paymentStatus !== PAYMENT_STATUS.PENDING) return;
        
        this.attempts++;
        
        try {
          // 设置检查状态超时
          this.checkStatusTimeout = setTimeout(() => {
            throw new Error('检查支付状态超时');
          }, PAYMENT_CONFIG.CHECK_STATUS_TIMEOUT);
          
          const response = await fetch(`/api/check-payment?orderId=${this.orderId}`);
          clearTimeout(this.checkStatusTimeout);
          
          if (!response.ok) {
            throw new Error('网络请求失败');
          }
          
          const data = await response.json();
          
          if (data.error) {
            // 记录支付错误
            this.logEvent('PAYMENT_CHECK_ERROR', {
              orderId: this.orderId,
              error: data.error
            });
            
            // 如果是临时错误，继续轮询
            if (this.attempts < PAYMENT_CONFIG.MAX_ATTEMPTS) {
              return;
            }
            
            throw new Error(data.error);
          }
          
          if (data.paid) {
            this.handlePaymentSuccess(data.tradeNo);
          } else if (this.attempts >= PAYMENT_CONFIG.MAX_ATTEMPTS) {
            this.handlePaymentExpired();
          }
          
        } catch (error) {
          clearTimeout(this.checkStatusTimeout);
          
          // 记录检查支付状态时的错误
          this.logEvent('PAYMENT_CHECK_ERROR', {
            orderId: this.orderId,
            attempt: this.attempts,
            error: error.message
          });
          
          // 如果达到最大尝试次数，标记为失败
          if (this.attempts >= PAYMENT_CONFIG.MAX_ATTEMPTS) {
            this.handlePaymentError(error);
          }
        }
      }
      
      // 处理支付成功
      handlePaymentSuccess(tradeNo) {
        this.paymentStatus = PAYMENT_STATUS.SUCCESS;
        this.stopPolling();
        
        // 记录支付成功事件
        this.logEvent('PAYMENT_SUCCESS', {
          orderId: this.orderId,
          tradeNo: tradeNo,
          amount: this.amount
        });
        
        this.setStatus(PAYMENT_STATUS.SUCCESS, '支付成功！正在跳转...');
        
        // 实际应用中，这里可以跳转到成功页面
        setTimeout(() => {
          this.setStatus(PAYMENT_STATUS.SUCCESS, '支付成功！感谢您的购买');
        }, 1500);
      }
      
      // 处理支付过期
      handlePaymentExpired() {
        this.paymentStatus = PAYMENT_STATUS.EXPIRED;
        this.stopPolling();
        
        this.logEvent('PAYMENT_EXPIRED', {
          orderId: this.orderId,
          amount: this.amount
        });
        
        this.setStatus(PAYMENT_STATUS.EXPIRED, '支付超时，请重新下单');
        
        // 显示重试按钮
        const retryBtn = document.createElement('button');
        retryBtn.className = 'retry-btn';
        retryBtn.textContent = '重新支付';
        retryBtn.addEventListener('click', () => {
          this.resetPayment();
          this.init();
        });
        this.statusEl.appendChild(retryBtn);
      }
      
      // 处理支付错误
      handlePaymentError(error) {
        this.paymentStatus = PAYMENT_STATUS.ERROR;
        this.stopPolling();
        
        this.logEvent('PAYMENT_ERROR', {
          orderId: this.orderId,
          amount: this.amount,
          error: error.message
        });
        
        this.setStatus(PAYMENT_STATUS.ERROR, `支付异常: ${error.message}`);
        
        // 显示错误详情
        this.errorDetailsEl.textContent = `错误代码: ${error.code || 'UNKNOWN'}`;
        this.errorDetailsEl.style.display = 'block';
        
        // 显示重试按钮
        const retryBtn = document.createElement('button');
        retryBtn.className = 'retry-btn';
        retryBtn.textContent = '重试支付';
        retryBtn.addEventListener('click', () => {
          this.resetPayment();
          this.init();
        });
        this.statusEl.appendChild(retryBtn);
      }
      
      // 重置支付状态
      resetPayment() {
        this.paymentStatus = PAYMENT_STATUS.PENDING;
        this.attempts = 0;
        
        // 清除二维码
        this.qrcodeEl.innerHTML = '';
        
        // 重置状态显示
        this.statusEl.className = 'status waiting';
        this.statusEl.innerHTML = `
          <div class="loading">
            <div class="spinner"></div>
            等待支付...
          </div>
        `;
        
        // 隐藏错误详情
        this.errorDetailsEl.style.display = 'none';
      }
      
      // 设置支付状态
      setStatus(status, message) {
        this.paymentStatus = status;
        
        switch(status) {
          case PAYMENT_STATUS.PENDING:
            this.statusEl.className = 'status waiting';
            break;
          case PAYMENT_STATUS.SUCCESS:
            this.statusEl.className = 'status success';
            break;
          case PAYMENT_STATUS.FAILED:
          case PAYMENT_STATUS.EXPIRED:
            this.statusEl.className = 'status error';
            break;
          case PAYMENT_STATUS.ERROR:
            this.statusEl.className = 'status error';
            break;
        }
        
        this.statusEl.innerHTML = message;
      }
      
      // 记录支付事件（可发送到后端日志）
      logEvent(eventType, data) {
        console.log(`[支付事件] ${eventType}`, {
          timestamp: new Date().toISOString(),
          ...data
        });
        
        // 将关键事件发送到后端记录
        fetch('/api/log-payment-event', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ eventType, ...data })
        });
      }
      
      // 错误处理
      handleError(error, context) {
        this.stopPolling();
        
        this.logEvent('INIT_ERROR', {
          context,
          error: error.message,
          stack: error.stack
        });
        
        console.error(`[${context}]`, error);
        
        this.setStatus(PAYMENT_STATUS.ERROR, `系统错误: ${error.message}`);
        
        // 显示重试按钮
        const retryBtn = document.createElement('button');
        retryBtn.className = 'retry-btn';
        retryBtn.textContent = '重新尝试';
        retryBtn.addEventListener('click', () => {
          this.resetPayment();
          this.init();
        });
        this.statusEl.appendChild(retryBtn);
      }
    }
    
    // 初始化支付
    document.addEventListener('DOMContentLoaded', () => {
      const paymentService = new PaymentService();
      paymentService.init().catch(error => {
        paymentService.handleError(error, '初始化失败');
      });
      
      // 处理页面卸载，记录用户行为
      window.addEventListener('beforeunload', () => {
        if (paymentService.paymentStatus === PAYMENT_STATUS.PENDING) {
          paymentService.logEvent('PAGE_UNLOAD', {
            orderId: paymentService.orderId,
            amount: paymentService.amount,
            attempts: paymentService.attempts
          });
        }
      });
    });
  </script>
</body>
</html>
```

## 四、关键流程与最佳实践

### 1. 支付宝异步通知处理流程

1. **接收通知**：支付宝通过POST请求发送异步通知到指定的notify_url 
2. **验证签名**：使用支付宝公钥验证通知的签名，确保请求来自支付宝 
3. **检查交易状态**：只处理TRADE_SUCCESS和TRADE_FINISHED状态的交易 
4. **防止重复处理**：检查订单是否已被处理，避免重复更新订单状态 
5. **验证交易金额**：确保通知中的金额与订单金额一致 
6. **更新订单状态**：将订单状态更新为已支付，并记录交易号等信息 
7. **返回响应**：必须返回'success'字符串，否则支付宝会重复发送通知 

### 2. 错误处理策略

1. **网络超时处理**：
   - 对于支付宝API调用设置合理的超时时间
   - 遇到超时错误时，先查询订单状态再决定是否重试 
   - 记录超时异常并考虑重试机制

2. **异常分类处理**：
   - **用户端错误**（如参数错误）：返回明确的错误信息
   - **服务端错误**（如支付宝服务不可用）：记录日志并提示用户稍后重试
   - **严重错误**（如签名验证失败）：记录详细日志并告警 

3. **重试机制**：
   - 对于临时性错误（如网络问题），实现指数退避重试
   - 对于支付宝返回的特定错误码，参考官方文档确定是否可重试

### 3. 日志记录最佳实践

1. **关键节点日志**：
   - 记录订单创建、支付成功、支付失败等关键事件 
   - 包含订单号、支付金额、交易号等必要信息 

2. **结构化日志**：
   - 使用JSON格式记录日志，便于后续分析
   - 包含时间戳、事件类型、相关ID等字段 

3. **错误日志分级**：
   - INFO：正常业务流程
   - WARN：潜在问题（如订单查询失败但可重试）
   - ERROR：需要立即关注的问题（如签名验证失败）

4. **敏感信息处理**：
   - 不记录完整的支付宝私钥等敏感信息
   - 对用户个人信息进行脱敏处理

### 4. 安全措施

1. **签名验证**：
   - 所有来自支付宝的请求必须验证签名
   - 使用支付宝提供的公钥验证，不要使用自己的私钥 

2. **HTTPS强制**：
   - 所有与支付宝相关的通信必须通过HTTPS
   - 避免在HTTP页面中嵌入支付功能 

3. **订单状态验证**：
   - 支付成功后，必须验证订单状态是否已处理
   - 防止重复支付和通知重放攻击

4. **敏感信息保护**：
   - 支付宝私钥等敏感信息必须通过环境变量配置
   - 不要将敏感信息硬编码在代码中或提交到版本控制

## 五、测试与部署建议

1. **沙箱环境测试**：
   - 使用支付宝提供的沙箱环境进行完整流程测试
   - 沙箱环境有专门的测试账号和支付工具

2. **模拟异步通知**：
   - 开发阶段模拟支付宝异步通知，测试各种场景
   - 特别测试签名验证、重复通知等边界情况

3. **日志监控**：
   - 设置日志监控，对ERROR级别的日志进行告警
   - 定期分析支付日志，发现潜在问题

4. **生产环境验证**：
   - 首次上线前进行小流量测试
   - 验证异步通知机制是否正常工作

通过以上实现，您的网页将具备完整的支付宝支付能力，包括安全的支付流程、完善的错误处理机制、详细的日志记录以及可靠的异步通知处理，能够满足生产环境的需求。