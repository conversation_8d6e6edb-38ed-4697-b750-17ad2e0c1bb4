const { createPaymentOrder, queryOrderStatus } = require('../services/alipayService');
const { alipayLogger, logPaymentEvent } = require('../services/logger');

// 创建支付订单
exports.createOrder = async (req, res) => {
  const { photoId, price, userEmail, photoTitle } = req.body;
  
  // 生成唯一订单号
  const outTradeNo = `PHOTO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // 参数验证
  if (!photoId || !price || !userEmail || !photoTitle) {
    alipayLogger.warn('创建订单参数缺失', { body: req.body, ip: req.ip });
    return res.status(400).json({ 
      success: false, 
      message: '缺少必要参数: photoId, price, userEmail, photoTitle' 
    });
  }
  
  // 金额验证
  const amountNum = parseFloat(price);
  if (isNaN(amountNum) || amountNum <= 0 || amountNum > 1000) {
    alipayLogger.warn('无效的金额', { amount: price, ip: req.ip });
    return res.status(400).json({ 
      success: false, 
      message: '无效的金额，金额必须在0.01-1000之间' 
    });
  }
  
  // 邮箱验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(userEmail)) {
    alipayLogger.warn('无效的邮箱格式', { email: userEmail, ip: req.ip });
    return res.status(400).json({ 
      success: false, 
      message: '无效的邮箱格式' 
    });
  }
  
  try {
    // 记录创建订单请求
    logPaymentEvent('CREATE_ORDER_REQUEST', outTradeNo, amountNum, {
      photoId,
      photoTitle,
      userEmail: userEmail.replace(/(.{3}).*(@.*)/, '$1***$2'), // 邮箱脱敏
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    const result = await createPaymentOrder(
      outTradeNo, 
      amountNum, 
      `摄影作品：${photoTitle}`,
      userEmail
    );
    
    if (result.success) {
      res.json({
        success: true,
        orderNo: outTradeNo,
        qrCode: result.qrCode
      });
    } else {
      // 记录业务层错误
      logPaymentEvent('CREATE_ORDER_ERROR', outTradeNo, amountNum, {
        errorCode: result.code,
        errorMessage: result.message,
        photoId,
        ip: req.ip
      });
      
      res.status(400).json(result);
    }
  } catch (error) {
    alipayLogger.error('创建订单内部错误', { 
      error: error.message, 
      stack: error.stack,
      body: req.body,
      ip: req.ip
    });
    
    res.status(500).json({ 
      success: false, 
      message: '服务器内部错误，请稍后重试' 
    });
  }
};

// 查询订单状态
exports.checkPayment = async (req, res) => {
  const { orderNo } = req.params;
  
  if (!orderNo) {
    return res.status(400).json({ 
      success: false, 
      message: '缺少orderNo参数' 
    });
  }
  
  // 订单号格式验证
  if (!orderNo.startsWith('PHOTO_') || orderNo.length < 20) {
    alipayLogger.warn('无效的订单号格式', { orderNo, ip: req.ip });
    return res.status(400).json({
      success: false,
      message: '无效的订单号格式'
    });
  }
  
  try {
    const result = await queryOrderStatus(orderNo);
    
    if (result.error) {
      alipayLogger.warn('查询支付状态失败', {
        orderNo,
        error: result.error,
        ip: req.ip
      });
      
      return res.json({ 
        success: false,
        paid: false,
        error: result.error
      });
    }
    
    // 记录查询事件
    logPaymentEvent('PAYMENT_STATUS_QUERY', orderNo, result.totalAmount, {
      paid: result.paid,
      tradeStatus: result.tradeStatus,
      ip: req.ip
    });
    
    res.json({ 
      success: true,
      paid: result.paid, 
      tradeNo: result.tradeNo,
      status: result.tradeStatus
    });
  } catch (error) {
    alipayLogger.error('查询支付状态错误', { 
      error: error.message, 
      stack: error.stack,
      orderNo,
      ip: req.ip
    });
    
    res.status(500).json({ 
      success: false,
      paid: false, 
      error: '查询服务暂时不可用' 
    });
  }
};

// 订单列表（管理功能）
exports.getOrders = async (req, res) => {
  try {
    // 这里应该从数据库获取订单列表
    // 简化实现，返回内存中的订单
    const { alipayService } = require('../services/alipayService');
    
    // 注意：实际生产环境中不应该暴露所有订单信息
    res.json({
      success: true,
      message: '此功能需要管理员权限'
    });
  } catch (error) {
    alipayLogger.error('获取订单列表失败', {
      error: error.message,
      ip: req.ip
    });
    
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
};