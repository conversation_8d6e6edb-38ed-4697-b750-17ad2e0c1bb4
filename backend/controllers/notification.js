const { handleNotification } = require('../services/alipayService');
const { alipayLogger, logSecurityEvent } = require('../services/logger');
const nodemailer = require('nodemailer');

// 邮件配置
let transporter = null;

// 初始化邮件传输器
const initEmailTransporter = () => {
  if (!transporter && process.env.SMTP_HOST) {
    transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }
};

// 处理支付宝异步通知 
exports.handleAlipayNotify = async (req, res) => {
  const startTime = Date.now();
  
  try {
    // 记录通知接收
    alipayLogger.info('收到支付宝异步通知', { 
      body: req.body,
      headers: {
        'content-type': req.get('content-type'),
        'user-agent': req.get('user-agent')
      },
      ip: req.ip
    });
    
    // 基本安全检查
    if (!req.body || Object.keys(req.body).length === 0) {
      logSecurityEvent('EMPTY_NOTIFY_BODY', { ip: req.ip });
      return res.status(400).send('fail');
    }
    
    // 检查必要字段
    const requiredFields = ['sign', 'sign_type', 'out_trade_no', 'trade_status'];
    const missingFields = requiredFields.filter(field => !req.body[field]);
    
    if (missingFields.length > 0) {
      logSecurityEvent('MISSING_NOTIFY_FIELDS', { 
        missingFields, 
        body: req.body,
        ip: req.ip 
      });
      return res.status(400).send('fail');
    }
    
    // 处理通知
    const result = await handleNotification(req.body);
    
    if (result.success) {
      // 如果支付成功，触发邮件发送
      if (req.body.trade_status === 'TRADE_SUCCESS') {
        await sendPaymentSuccessEmail(req.body.out_trade_no, req.body);
      }
      
      // 记录处理时间
      const processingTime = Date.now() - startTime;
      alipayLogger.info('异步通知处理成功', {
        orderNo: req.body.out_trade_no,
        processingTime: `${processingTime}ms`
      });
      
      // 必须返回success，否则支付宝会重复通知
      res.send('success');
    } else {
      alipayLogger.error('处理异步通知失败', { 
        message: result.message,
        body: req.body,
        ip: req.ip
      });
      
      // 返回错误，让支付宝重试
      res.status(500).send('fail');
    }
  } catch (error) {
    alipayLogger.error('处理异步通知时发生未捕获异常', { 
      error: error.message, 
      stack: error.stack,
      body: req.body,
      ip: req.ip
    });
    
    // 返回错误，让支付宝重试
    res.status(500).send('fail');
  }
};

// 发送支付成功邮件
const sendPaymentSuccessEmail = async (orderNo, notifyData) => {
  try {
    // 获取订单信息
    const { getOrderById } = require('../services/alipayService');
    const order = await getOrderById(orderNo);
    
    if (!order || !order.userEmail) {
      alipayLogger.warn('无法发送邮件，订单或邮箱信息缺失', { orderNo });
      return;
    }
    
    // 初始化邮件传输器
    initEmailTransporter();
    
    if (!transporter) {
      alipayLogger.warn('邮件服务未配置，跳过邮件发送', { orderNo });
      return;
    }
    
    const mailOptions = {
      from: process.env.SMTP_USER,
      to: order.userEmail,
      subject: `支付成功通知 - ${order.subject || '高清摄影作品'}`,
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #007bff;">支付成功！</h2>
          <p>亲爱的客户，您好！</p>
          <p>您购买的摄影作品《${order.subject || '高清原图'}》支付已成功完成。</p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">订单详情</h3>
            <p><strong>订单号：</strong>${orderNo}</p>
            <p><strong>作品名称：</strong>${order.subject || '高清摄影作品'}</p>
            <p><strong>支付金额：</strong>¥${notifyData.total_amount}</p>
            <p><strong>交易号：</strong>${notifyData.trade_no}</p>
            <p><strong>支付时间：</strong>${new Date(notifyData.gmt_payment || notifyData.notify_time).toLocaleString('zh-CN')}</p>
          </div>
          
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #856404;">📁 获取高清作品文件</h3>
            <p>由于文件较大，请通过以下方式获取您的高清作品：</p>
            <ul>
              <li>联系客服邮箱：<strong><EMAIL></strong></li>
              <li>客服电话：<strong>86-190-4276-2541</strong></li>
              <li>请提供您的订单号：<strong>${orderNo}</strong></li>
            </ul>
            <p><small>我们会在24小时内通过邮件或网盘链接的方式发送给您。</small></p>
          </div>
          
          <div style="border-top: 1px solid #dee2e6; padding-top: 20px; margin-top: 30px;">
            <h3>版权说明</h3>
            <p style="font-size: 14px; color: #6c757d;">
              • 该作品授权您个人使用，包括打印、收藏等<br>
              • 如需商业用途，请联系我们获取商业授权<br>
              • 请勿二次销售或未经授权的商业使用
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
            <p style="color: #6c757d;">感谢您对艺境光影摄影工作室的支持！</p>
            <p style="font-size: 12px; color: #adb5bd;">此邮件由系统自动发送，请勿回复</p>
          </div>
        </div>
      `
    };
    
    await transporter.sendMail(mailOptions);
    
    alipayLogger.info('支付成功邮件发送成功', {
      orderNo,
      email: order.userEmail.replace(/(.{3}).*(@.*)/, '$1***$2'), // 邮箱脱敏
      tradeNo: notifyData.trade_no
    });
    
  } catch (error) {
    alipayLogger.error('发送支付成功邮件失败', {
      error: error.message,
      stack: error.stack,
      orderNo
    });
    // 邮件发送失败不应该影响支付流程，只记录错误
  }
};

// 处理支付宝同步返回（可选）
exports.handleAlipayReturn = async (req, res) => {
  try {
    alipayLogger.info('收到支付宝同步返回', { 
      query: req.query,
      ip: req.ip 
    });
    
    // 同步返回也需要验证签名
    const { verifySign } = require('../services/alipayService');
    const isValid = verifySign(req.query, req.query.sign);
    
    if (isValid && req.query.trade_status === 'TRADE_SUCCESS') {
      // 支付成功，可以跳转到成功页面
      res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment-success?order=${req.query.out_trade_no}`);
    } else {
      // 验证失败或支付未成功
      logSecurityEvent('RETURN_SIGNATURE_FAILED', { query: req.query, ip: req.ip });
      res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment-failed`);
    }
  } catch (error) {
    alipayLogger.error('处理支付宝同步返回失败', {
      error: error.message,
      query: req.query,
      ip: req.ip
    });
    
    res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment-error`);
  }
};

module.exports = {
  handleAlipayNotify: exports.handleAlipayNotify,
  handleAlipayReturn: exports.handleAlipayReturn
};