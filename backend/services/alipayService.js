const AlipaySdk = require('alipay-sdk').default;
const AlipayFormData = require('alipay-sdk/lib/form').default;
const crypto = require('crypto');
const { alipayLogger, logAlipayApiCall, logPaymentEvent, logSecurityEvent } = require('./logger');
const alipayConfig = require('../config/alipay');

// 创建支付宝SDK实例
const alipaySdk = new AlipaySdk({
  appId: alipayConfig.appId,
  privateKey: alipayConfig.privateKey,
  signType: alipayConfig.signType,
  gateway: alipayConfig.gateway,
  alipayPublicKey: alipayConfig.publicKey
});

// 验证支付宝通知签名
const verifySign = (params, sign) => {
  try {
    // 移除sign和sign_type参数
    const { sign: _, sign_type: __, ...paramsWithoutSign } = params;
    
    // 将参数按字母排序
    const sortedParams = Object.keys(paramsWithoutSign)
      .sort()
      .reduce((acc, key) => {
        // 跳过空值
        if (paramsWithoutSign[key] !== '' && paramsWithoutSign[key] !== null && paramsWithoutSign[key] !== undefined) {
          acc[key] = paramsWithoutSign[key];
        }
        return acc;
      }, {});
    
    // 构建待签名字符串
    const stringToSign = Object.keys(sortedParams)
      .map(key => `${key}=${sortedParams[key]}`)
      .join('&');
    
    alipayLogger.debug('签名验证', { stringToSign });
    
    // 使用支付宝公钥验证签名
    const verifier = crypto.createVerify('RSA-SHA256');
    verifier.update(stringToSign);
    
    // 处理公钥格式
    let publicKey = alipayConfig.publicKey;
    if (!publicKey.includes('-----BEGIN PUBLIC KEY-----')) {
      publicKey = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`;
    }
    
    const isValid = verifier.verify(publicKey, sign, 'base64');
    
    if (!isValid) {
      logSecurityEvent('SIGNATURE_VERIFICATION_FAILED', {
        params: paramsWithoutSign,
        sign,
        stringToSign
      });
    }
    
    return isValid;
  } catch (error) {
    alipayLogger.error('签名验证失败', { 
      error: error.message, 
      stack: error.stack,
      params 
    });
    return false;
  }
};

// 处理支付宝异步通知 
const handleNotification = async (params) => {
  try {
    alipayLogger.info('收到支付宝异步通知', { params });
    
    // 1. 验证签名
    if (!verifySign(params, params.sign)) {
      alipayLogger.error('异步通知签名验证失败', { params });
      logSecurityEvent('NOTIFY_SIGNATURE_FAILED', { params });
      return { success: false, message: '签名验证失败' };
    }
    
    // 2. 检查交易状态
    if (params.trade_status !== 'TRADE_SUCCESS' && params.trade_status !== 'TRADE_FINISHED') {
      alipayLogger.info('非成功交易状态', { 
        tradeStatus: params.trade_status, 
        orderId: params.out_trade_no 
      });
      return { success: true }; // 返回成功，避免支付宝重复通知
    }
    
    // 3. 检查订单是否已处理（防止重复通知）
    const isOrderProcessed = await checkIfOrderProcessed(params.out_trade_no);
    if (isOrderProcessed) {
      alipayLogger.info('订单已处理，忽略重复通知', { orderId: params.out_trade_no });
      return { success: true };
    }
    
    // 4. 验证金额
    const order = await getOrderById(params.out_trade_no);
    if (!order) {
      alipayLogger.error('订单不存在', { orderId: params.out_trade_no });
      return { success: false, message: '订单不存在' };
    }
    
    const expectedAmount = parseFloat(order.price);
    const actualAmount = parseFloat(params.total_amount);
    
    if (Math.abs(expectedAmount - actualAmount) > 0.01) { // 允许1分钱的误差
      alipayLogger.error('订单金额不匹配', { 
        orderId: params.out_trade_no, 
        expected: expectedAmount, 
        actual: actualAmount 
      });
      logSecurityEvent('AMOUNT_MISMATCH', {
        orderId: params.out_trade_no,
        expectedAmount,
        actualAmount
      });
      return { success: false, message: '金额不匹配' };
    }
    
    // 5. 更新订单状态
    await updateOrderStatus(params.out_trade_no, 'paid', {
      transactionId: params.trade_no,
      buyerId: params.buyer_id,
      paymentTime: new Date(params.gmt_payment || params.notify_time),
      totalAmount: params.total_amount
    });
    
    // 6. 记录支付成功日志 
    logPaymentEvent('PAYMENT_SUCCESS', params.out_trade_no, params.total_amount, {
      tradeNo: params.trade_no,
      buyerId: params.buyer_id,
      sellerId: params.seller_id
    });
    
    // 7. 触发后续业务逻辑（如发送邮件）
    await triggerPostPaymentActions(order, params);
    
    return { success: true };
  } catch (error) {
    alipayLogger.error('处理异步通知时出错', { 
      error: error.message, 
      stack: error.stack,
      params 
    });
    return { success: false, message: '处理失败' };
  }
};

// 简化的数据库操作函数（实际项目中应连接真实数据库）
let orders = new Map();

// 检查订单是否已处理
const checkIfOrderProcessed = async (orderId) => {
  const order = orders.get(orderId);
  return order && order.status === 'paid';
};

// 获取订单信息
const getOrderById = async (orderId) => {
  return orders.get(orderId);
};

// 更新订单状态
const updateOrderStatus = async (orderId, status, details = {}) => {
  const order = orders.get(orderId);
  if (order) {
    const updatedOrder = {
      ...order,
      status,
      ...details,
      updatedAt: new Date()
    };
    orders.set(orderId, updatedOrder);
    alipayLogger.info('更新订单状态', { orderId, status, details });
    return updatedOrder;
  }
  return null;
};

// 保存订单
const saveOrder = async (orderData) => {
  const order = {
    ...orderData,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  orders.set(orderData.orderNo, order);
  alipayLogger.info('保存订单', { orderId: orderData.orderNo });
  return order;
};

// 创建支付订单
const createPaymentOrder = async (orderId, amount, subject, userEmail, storeId = 'web') => {
  try {
    // 参数验证
    if (!orderId || !amount || !subject || !userEmail) {
      throw new Error('缺少必要参数');
    }
    
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0 || amountNum > 1000) {
      throw new Error('无效的金额');
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      throw new Error('无效的邮箱格式');
    }
    
    const bizContent = {
      out_trade_no: orderId,
      total_amount: amountNum.toFixed(2),
      subject: subject,
      body: `购买高清摄影作品 - ${subject}`,
      store_id: storeId,
      timeout_express: '10m', // 10分钟支付超时
      notify_url: alipayConfig.notifyUrl
    };
    
    // 记录API调用请求
    logAlipayApiCall('alipay.trade.precreate', { 
      orderId, 
      amount: amountNum.toFixed(2),
      subject 
    }, { status: 'requesting' });
    
    const result = await alipaySdk.exec('alipay.trade.precreate', {
      bizContent
    });
    
    // 记录API调用响应
    logAlipayApiCall('alipay.trade.precreate', { orderId }, result);
    
    if (result.code === '10000') {
      // 保存订单信息
      await saveOrder({
        orderNo: orderId,
        price: amountNum,
        userEmail,
        subject,
        qrCode: result.qr_code,
        status: 'pending'
      });
      
      // 记录订单创建成功
      logPaymentEvent('ORDER_CREATED', orderId, amountNum.toFixed(2), {
        qrCode: result.qr_code ? '[GENERATED]' : '[FAILED]',
        subject
      });
      
      return { 
        success: true, 
        qrCode: result.qr_code,
        orderNo: orderId
      };
    } else {
      // 记录支付系统错误
      logPaymentEvent('ORDER_CREATION_FAILED', orderId, amountNum.toFixed(2), {
        errorCode: result.code,
        errorMsg: result.msg,
        subCode: result.sub_code,
        subMsg: result.sub_msg
      });
      
      // 对于超时或未知异常，记录并考虑重试机制
      if (result.code === '20000') { // 服务不可用
        alipayLogger.warn('支付宝服务暂时不可用，建议重试', { orderId, result });
      }
      
      return { 
        success: false, 
        message: result.sub_msg || result.msg || '创建支付订单失败',
        code: result.code
      };
    }
  } catch (error) {
    alipayLogger.error('创建支付订单时出错', { 
      error: error.message, 
      stack: error.stack,
      orderId,
      amount
    });
    
    // 记录异常
    logPaymentEvent('EXCEPTION', orderId, amount, {
      errorType: 'API_CALL_ERROR',
      errorMessage: error.message
    });
    
    return { 
      success: false, 
      message: error.message || '支付服务暂时不可用，请稍后重试',
      code: 'SERVER_ERROR'
    };
  }
};

// 查询订单状态
const queryOrderStatus = async (orderId) => {
  try {
    const result = await alipaySdk.exec('alipay.trade.query', {
      bizContent: {
        out_trade_no: orderId
      }
    });
    
    logAlipayApiCall('alipay.trade.query', { orderId }, result);
    
    if (result.code === '10000') {
      const isPaid = result.trade_status === 'TRADE_SUCCESS' || result.trade_status === 'TRADE_FINISHED';
      return { 
        paid: isPaid, 
        tradeNo: result.trade_no,
        tradeStatus: result.trade_status,
        totalAmount: result.total_amount
      };
    }
    
    // 记录查询错误
    logPaymentEvent('ORDER_QUERY_FAILED', orderId, null, {
      errorCode: result.code,
      errorMsg: result.msg,
      subCode: result.sub_code,
      subMsg: result.sub_msg
    });
    
    return { 
      paid: false, 
      error: result.sub_msg || result.msg || '查询失败' 
    };
  } catch (error) {
    alipayLogger.error('查询订单状态时出错', { 
      error: error.message, 
      stack: error.stack,
      orderId
    });
    
    return { paid: false, error: '查询服务暂时不可用' };
  }
};

// 触发支付后续动作
const triggerPostPaymentActions = async (order, notifyParams) => {
  try {
    // 这里可以触发邮件发送、库存更新等后续业务逻辑
    alipayLogger.info('触发支付后续动作', {
      orderId: order.orderNo,
      userEmail: order.userEmail,
      tradeNo: notifyParams.trade_no
    });
    
    // 示例：发送邮件通知（实际实现需要集成邮件服务）
    // await sendPhotoToEmail(order.userEmail, order);
    
  } catch (error) {
    alipayLogger.error('执行支付后续动作失败', {
      error: error.message,
      orderId: order.orderNo
    });
  }
};

module.exports = {
  alipaySdk,
  createPaymentOrder,
  queryOrderStatus,
  handleNotification,
  verifySign,
  saveOrder,
  updateOrderStatus,
  getOrderById
};