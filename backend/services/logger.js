const winston = require('winston');

// 创建支付宝专用日志记录器 - 统一使用控制台输出
const alipayLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json() // 使用 JSON 格式便于日志处理和查看
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// 记录支付关键日志，包括订单号、支付类型和交易信息 
const logPaymentEvent = (eventType, orderId, amount, additionalData = {}) => {
  alipayLogger.info(`Payment Event: ${eventType}`, {
    orderId,
    amount,
    ...additionalData,
    timestamp: new Date().toISOString()
  });
};

// 记录支付宝API调用日志
const logAlipayApiCall = (apiName, request, response) => {
  alipayLogger.info(`Alipay API Call: ${apiName}`, {
    request: {
      ...request,
      // 不记录敏感信息
      privateKey: request.privateKey ? '[HIDDEN]' : undefined
    },
    response: {
      ...response,
      // 限制响应长度
      data: typeof response.data === 'string' && response.data.length > 1000 
        ? response.data.substring(0, 1000) + '...[TRUNCATED]'
        : response.data
    }
  });
};

// 记录SQL日志（在实际项目中与数据库操作集成）
const logSqlQuery = (query, params) => {
  alipayLogger.debug('SQL Query', { query, params });
};

// 记录安全事件
const logSecurityEvent = (eventType, details) => {
  alipayLogger.warn(`Security Event: ${eventType}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  alipayLogger,
  logPaymentEvent,
  logAlipayApiCall,
  logSqlQuery,
  logSecurityEvent
};