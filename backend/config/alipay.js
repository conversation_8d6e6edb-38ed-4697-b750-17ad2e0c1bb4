require('dotenv').config();

module.exports = {
  appId: process.env.ALIPAY_APP_ID,
  privateKey: process.env.ALIPAY_PRIVATE_KEY,
  publicKey: process.env.ALIPAY_PUBLIC_KEY, // 支付宝公钥
  notifyUrl: process.env.ALIPAY_NOTIFY_URL || `${process.env.BASE_URL || 'http://localhost:3000'}/api/alipay-notify`,
  returnUrl: process.env.ALIPAY_RETURN_URL || `${process.env.BASE_URL || 'http://localhost:3000'}/payment-success`,
  gateway: process.env.NODE_ENV === 'production' 
    ? 'https://openapi.alipay.com/gateway.do' 
    : 'https://openapi.alipaydev.com/gateway.do', // 沙箱环境
  signType: 'RSA2',
  charset: 'utf-8',
  version: '1.0'
};