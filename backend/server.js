const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// 导入服务和控制器
const { alipayLogger, logPaymentEvent } = require('./services/logger');
const paymentController = require('./controllers/payment');
const notificationController = require('./controllers/notification');

// 环境变量检查函数
function checkRequiredEnvVars() {
    const requiredVars = [
        'SMTP_HOST',
        'SMTP_PORT',
        'SMTP_USER',
        'SMTP_PASS',
        'ALIPAY_APP_ID'
    ];

    const missingVars = [];

    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    }

    // 检查支付宝密钥配置
    const hasPrivateKey = process.env.ALIPAY_PRIVATE_KEY;
    const hasPublicKey = process.env.ALIPAY_PUBLIC_KEY;

    if (!hasPrivateKey) {
        missingVars.push('ALIPAY_PRIVATE_KEY - 密钥');
    }

    if (!hasPublicKey) {
        missingVars.push('ALIPAY_PUBLIC_KEY - 公钥');
    }

    if (missingVars.length > 0) {
        const errorMessage = `缺少必需的环境变量配置：\n${missingVars.map(v => `  - ${v}`).join('\n')}\n\n请在 .env 文件中配置这些环境变量，或者设置对应的系统环境变量。`;
        console.error('\n❌ 环境变量配置错误:');
        console.error(errorMessage);
        throw new Error(errorMessage);
    }

    console.log('✅ 所有必需的环境变量已正确配置');
}

// 检查环境变量
try {
    checkRequiredEnvVars();
} catch (error) {
    console.error('服务器启动失败:', error.message);
    process.exit(1);
}

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// CORS 配置 - 允许来自 Netlify 的请求
const corsOptions = {
    origin: [
        'http://localhost:3000',
        'http://localhost:5000',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:5000',
        // 添加你的 Netlify 域名
        'https://artisticrealm.netlify.app',
        // 如果有自定义域名，也添加进来
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 生产环境不需要静态文件服务（前端由 Netlify 提供）
if (process.env.NODE_ENV !== 'production') {
    app.use(express.static(path.join(__dirname, '../frontend')));
}

// 请求日志中间件
app.use((req, res, next) => {
    const start = Date.now();
    
    // 记录请求
    alipayLogger.info('Request received', { 
        method: req.method, 
        url: req.url,
        ip: req.ip || req.connection.remoteAddress,
        headers: {
            'user-agent': req.get('User-Agent'),
            'accept': req.get('Accept'),
            'content-type': req.get('Content-Type')
        },
        // 对于支付相关请求，记录更多信息
        body: req.url.includes('/api/create-payment') || req.url.includes('/api/alipay-notify') 
            ? req.body 
            : undefined
    });
    
    const originalSend = res.send;
    res.send = function(body) {
        const duration = Date.now() - start;
        alipayLogger.info('Response sent', { 
            method: req.method,
            url: req.url,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip || req.connection.remoteAddress
        });
        return originalSend.apply(res, [body]);
    };
    
    next();
});

// 支付相关路由 - 使用新的控制器
app.post('/api/create-payment', paymentController.createOrder);
app.get('/api/payment-status/:orderNo', paymentController.checkPayment);
app.post('/api/alipay-notify', notificationController.handleAlipayNotify);
app.get('/api/alipay-return', notificationController.handleAlipayReturn);

// 自动读取图片文件夹并生成作品数据
app.get('/api/photography-data', async (req, res) => {
    try {
        const imagesPath = path.join(__dirname, '../frontend/images');
        const photographyData = [];

        // 分类映射
        const categoryMap = {
            'Animal': 'animal',
            'Architectural': 'architecture',
            'Food': 'food',
            'Landscape': 'landscape',
            'Portrait': 'portrait',
            'Realistic': 'realistic'
        };

        // 分类中文名称
        const categoryNames = {
            'animal': '动物',
            'architecture': '建筑',
            'food': '食物',
            'landscape': '风景',
            'portrait': '人物',
            'realistic': '写实'
        };

        // 检查图片目录是否存在
        if (!fs.existsSync(imagesPath)) {
            alipayLogger.warn('图片目录不存在', { imagesPath });
            return res.json({
                success: true,
                data: [],
                count: 0,
                message: '图片目录不存在'
            });
        }

        // 读取所有分类文件夹
        const categories = fs.readdirSync(imagesPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        let idCounter = 1;

        for (const categoryFolder of categories) {
            const categoryKey = categoryMap[categoryFolder];
            if (!categoryKey) continue; // 跳过未映射的文件夹

            const categoryPath = path.join(imagesPath, categoryFolder);

            try {
                const files = fs.readdirSync(categoryPath)
                    .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file));

                for (const file of files) {
                    // 生成作品数据 - 新策略：所有图片免费浏览，下载收费2元
                    const photoData = {
                        id: idCounter++,
                        title: generateTitle(categoryKey, file),
                        description: generateDescription(categoryKey),
                        category: categoryKey,
                        price: 2, // 统一下载价格为2元人民币
                        isPaid: false, // 所有图片都可以免费浏览
                        image: `images/${categoryFolder}/${file}`
                    };

                    photographyData.push(photoData);
                }
            } catch (error) {
                alipayLogger.warn(`读取分类文件夹失败: ${categoryFolder}`, { 
                    error: error.message,
                    categoryPath 
                });
            }
        }

        // 记录作品数据加载
        alipayLogger.info('摄影作品数据加载成功', {
            totalCount: photographyData.length,
            categories: Object.keys(categoryMap).filter(cat => categories.includes(cat))
        });

        res.json({
            success: true,
            data: photographyData,
            count: photographyData.length
        });

    } catch (error) {
        alipayLogger.error('读取摄影作品数据失败', { 
            error: error.message,
            stack: error.stack 
        });
        
        res.status(500).json({
            success: false,
            message: '读取摄影作品数据失败'
        });
    }
});

// 生成作品标题
function generateTitle(category, filename) {
    const titles = {
        landscape: ['晨曦山峦', '湖光倒影', '云海奇观', '夕阳西下', '雪山之巅', '森林秘境'],
        portrait: ['优雅肖像', '街头瞬间', '光影人像', '情感写真', '时尚大片', '自然表情'],
        food: ['美食艺术', '甜品时光', '精致料理', '诱人美味', '色香味俱全', '烹饪艺术'],
        animal: ['野生精灵', '宠物写真', '自然生灵', '动物世界', '生命力量', '可爱瞬间'],
        architecture: ['现代建筑', '复古建筑', '钢筋森林', '建筑美学', '城市印象', '设计之美'],
        realistic: ['城市印象', '生活瞬间', '街角故事', '真实记录', '日常之美', '人文关怀']
    };

    const categoryTitles = titles[category] || ['摄影作品'];
    const randomTitle = categoryTitles[Math.floor(Math.random() * categoryTitles.length)];

    // 添加文件名的一部分作为唯一标识
    const fileId = filename.split('.')[0].slice(-4);
    return `${randomTitle} ${fileId}`;
}

// 生成作品描述
function generateDescription(category) {
    const descriptions = {
        landscape: [
            '清晨第一缕阳光洒向群山，金辉万丈，美不胜收',
            '静谧湖水倒映着蓝天白云，宛如仙境',
            '云海翻腾，山峦若隐若现，如梦如幻',
            '夕阳西下，天空被染成金黄色，温暖而宁静'
        ],
        portrait: [
            '光影交织下的人物肖像，诠释独特气质',
            '捕捉城市中人们的真实瞬间',
            '自然光线下的完美表情，展现内心世界',
            '时尚与艺术的完美结合，彰显个性魅力'
        ],
        food: [
            '精致摆盘，诱人美食的视觉盛宴',
            '诱人甜品，生活中的小确幸',
            '色香味俱全的料理艺术，满足视觉与味觉',
            '烹饪的艺术，每一道菜都是一件艺术品'
        ],
        animal: [
            '自然中的野生动物，展现生命的力量',
            '记录毛孩子们的可爱瞬间',
            '动物世界的精彩瞬间，生命的美好',
            '人与动物和谐相处的温馨画面'
        ],
        architecture: [
            '现代建筑的独特设计，展现城市的未来感',
            '复古建筑的独特设计，展现城市的历史感',
            '摩天大楼林立，现代都市的钢铁诗篇',
            '建筑美学与功能的完美结合'
        ],
        realistic: [
            '繁华都市中的真实面貌，展现城市生活的多样性',
            '记录日常生活中的真实片段，平凡却动人的时光',
            '街头巷尾的人情冷暖，每个角落都有故事',
            '真实记录生活的美好瞬间，发现平凡中的不平凡'
        ]
    };

    const categoryDescriptions = descriptions[category] || ['精美的摄影作品，值得收藏'];
    return categoryDescriptions[Math.floor(Math.random() * categoryDescriptions.length)];
}

// 健康检查接口
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        message: '服务运行正常',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development'
    });
});

// 首页路由
app.get('/', (req, res) => {
    if (process.env.NODE_ENV === 'production') {
        res.json({
            message: '艺境光影摄影工作室 API 服务',
            version: '2.0.0',
            status: 'running'
        });
    } else {
        res.sendFile(path.join(__dirname, '../frontend/index.html'));
    }
});

// 错误处理中间件
app.use((err, req, res, next) => {
    alipayLogger.error('全局错误处理', { 
        error: err.message, 
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip || req.connection.remoteAddress
    });
    
    res.status(500).json({ 
        success: false, 
        message: process.env.NODE_ENV === 'production' 
            ? '服务器内部错误' 
            : err.message
    });
});

// 404 处理
app.use('*', (req, res) => {
    alipayLogger.warn('404 Not Found', {
        url: req.url,
        method: req.method,
        ip: req.ip || req.connection.remoteAddress
    });
    
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 启动服务器
const server = app.listen(PORT, () => {
    alipayLogger.info(`服务器启动成功`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
    });
    
    console.log(`🚀 服务器运行在端口 ${PORT}`);
    console.log(`📊 环境: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔧 支付宝网关: ${process.env.NODE_ENV === 'production' ? '生产环境' : '沙箱环境'}`);
    console.log(`📝 日志文件: ./logs/alipay.log`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    alipayLogger.info('收到 SIGTERM 信号，开始优雅关闭服务器');
    
    server.close(() => {
        alipayLogger.info('服务器已关闭');
        process.exit(0);
    });
});

// 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
    alipayLogger.error('未处理的Promise拒绝', { 
        reason: reason ? reason.message : '未知原因',
        stack: reason && reason.stack
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    alipayLogger.error('未捕获的异常', { 
        error: error.message, 
        stack: error.stack
    });
    
    // 在记录错误后安全退出
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});

module.exports = app;