/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5em;
    font-weight: 700;
    color: #2c3e50;
}

.nav-logo i {
    margin-right: 10px;
    color: #e74c3c;
    font-size: 1.2em;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0 20px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #e74c3c;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: #e74c3c;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* 主页面英雄区域 */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="camera" patternUnits="userSpaceOnUse" width="50" height="50"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23camera)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero h1 {
    font-size: 3.5em;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.3em;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
    text-decoration: none;
    padding: 15px 40px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1em;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 作品展示区域 */
.gallery-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.gallery-section h2 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 50px;
    color: #2c3e50;
    font-weight: 700;
}

.category-tabs {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 50px;
    gap: 10px;
}

.tab-button {
    background: white;
    border: 2px solid #e0e0e0;
    color: #666;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95em;
}

.tab-button:hover,
.tab-button.active {
    background: #e74c3c;
    border-color: #e74c3c;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.gallery-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item-content {
    padding: 20px;
}

.gallery-item h3 {
    font-size: 1.2em;
    margin-bottom: 10px;
    color: #2c3e50;
}

.gallery-item p {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.gallery-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-tag {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
}

.free-tag {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
}

.preview-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
}

.category-badge {
    background: #ecf0f1;
    color: #7f8c8d;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 500;
}

/* 关于我们区域 */
.about-section {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5em;
    margin-bottom: 30px;
    color: #2c3e50;
    font-weight: 700;
}

.about-text p {
    font-size: 1.1em;
    line-height: 1.8;
    color: #555;
    margin-bottom: 20px;
}

.about-image {
    text-align: center;
    position: relative;
}

.about-image::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 340px;
    height: 340px;
    border-radius: 50%;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    z-index: -1;
    opacity: 0.1;
}

.photographer-photo {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.photographer-photo:hover {
    transform: scale(1.05);
}

/* 联系方式区域 */
.contact-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 50px;
    color: #2c3e50;
    font-weight: 700;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.contact-item {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item i {
    font-size: 3em;
    color: #e74c3c;
    margin-bottom: 20px;
}

.contact-item h3 {
    font-size: 1.3em;
    margin-bottom: 15px;
    color: #2c3e50;
}

.contact-item p {
    color: #666;
    font-size: 1.1em;
}

/* 服务条款区域 */
.policy-section {
    padding: 100px 0;
    background: white;
}

.policy-section h2 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 50px;
    color: #2c3e50;
    font-weight: 700;
}

.policy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.policy-item {
    background: #f8f9fa;
    padding: 40px 30px;
    border-radius: 15px;
    border-left: 5px solid #e74c3c;
}

.policy-item h3 {
    font-size: 1.3em;
    margin-bottom: 20px;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.policy-item h3 i {
    margin-right: 10px;
    color: #e74c3c;
}

.policy-item p {
    color: #555;
    line-height: 1.7;
}

/* 页脚样式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 1.3em;
    margin-bottom: 20px;
    color: #ecf0f1;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #e74c3c;
}

.footer-section p {
    color: #bdc3c7;
    margin-bottom: 10px;
}

.footer-section p i {
    margin-right: 10px;
    color: #e74c3c;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 20px;
    text-align: center;
    color: #95a5a6;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 95vh;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    background-color: #f0f0f0;
    color: #333;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px 15px 0 0;
    text-align: center;
    flex-shrink: 0;
}

.modal-header h2 {
    margin-bottom: 10px;
    font-size: 1.5em;
}

.modal-header p {
    opacity: 0.9;
    font-size: 0.95em;
}

.modal-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

.payment-info {
    text-align: center;
}

.price-info {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.price {
    font-size: 2.5em;
    font-weight: 700;
    color: #e74c3c;
    display: block;
}

.price-desc {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

.email-input {
    margin-bottom: 30px;
    text-align: left;
}

.email-input label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.email-input input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.email-input input:focus {
    outline: none;
    border-color: #e74c3c;
}

.email-input small {
    color: #666;
    font-size: 0.85em;
    margin-top: 5px;
    display: block;
}

.payment-qr h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.2em;
}

.qr-code {
    margin-bottom: 15px;
}

.qr-code img {
    width: 200px;
    height: 200px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
}

.payment-tips {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 30px;
}

.payment-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-primary,
.btn-secondary {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-secondary {
    background: #ecf0f1;
    color: #666;
}

.btn-secondary:hover {
    background: #d5dbdb;
}

/* 成功弹窗样式 */
.success-content {
    text-align: center;
    padding: 50px 30px;
}

.success-icon {
    margin-bottom: 20px;
}

.success-icon i {
    font-size: 4em;
    color: #27ae60;
}

.success-content h2 {
    color: #27ae60;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.success-content p {
    color: #666;
    margin-bottom: 10px;
}

.email-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .hero h1 {
        font-size: 2.5em;
    }

    .hero p {
        font-size: 1.1em;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .policy-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .category-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .tab-button {
        white-space: nowrap;
        min-width: auto;
    }

    .modal-content {
        width: 95%;
        margin: 1% auto;
        max-height: 98vh;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 1.3em;
    }

    .modal-body {
        padding: 20px;
    }

    .payment-actions {
        flex-direction: column;
        margin-top: 20px;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
    }

    .qr-code img {
        width: 180px;
        height: 180px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .nav-container {
        padding: 0 15px;
    }

    .hero h1 {
        font-size: 2em;
    }

    .hero p {
        font-size: 1em;
    }

    .gallery-section h2,
    .about-text h2,
    .contact-section h2,
    .policy-section h2 {
        font-size: 2em;
    }

    .gallery-section,
    .about-section,
    .contact-section,
    .policy-section {
        padding: 60px 0;
    }

    .qr-code img {
        width: 150px;
        height: 150px;
    }

    .modal-content {
        margin: 0.5% auto;
        max-height: 99vh;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-header h2 {
        font-size: 1.2em;
        margin-bottom: 5px;
    }

    .modal-header p {
        font-size: 0.85em;
    }

    .modal-body {
        padding: 15px;
    }

    .price {
        font-size: 2em;
    }

    .payment-qr h3 {
        font-size: 1.1em;
        margin-bottom: 15px;
    }
}

/* 极小屏幕优化 (320px及以下) */
@media (max-width: 320px) {
    .modal-content {
        width: 98%;
        margin: 0.2% auto;
        max-height: 99.5vh;
    }

    .modal-header {
        padding: 10px;
    }

    .modal-header h2 {
        font-size: 1.1em;
    }

    .modal-body {
        padding: 10px;
    }

    .qr-code img {
        width: 120px;
        height: 120px;
    }

    .price {
        font-size: 1.8em;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 20px;
        font-size: 0.9em;
    }
}

/* 低高度屏幕优化 */
@media (max-height: 600px) {
    .modal-content {
        margin: 1% auto;
        max-height: 98vh;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .qr-code img {
        width: 140px;
        height: 140px;
    }

    .price-info {
        margin-bottom: 20px;
        padding: 15px;
    }

    .email-input {
        margin-bottom: 20px;
    }

    .payment-qr h3 {
        margin-bottom: 15px;
    }

    .payment-tips {
        margin-bottom: 20px;
    }
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 选中状态 */
::selection {
    background-color: #e74c3c;
    color: white;
}

/* 加载动画 */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 图片预览模态框样式 */
.preview-content {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
}

.preview-container {
    display: flex;
    gap: 30px;
    padding: 20px;
}

.preview-image-container {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
}

.preview-image-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
}

.preview-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px 0;
}

.preview-info h2 {
    font-size: 1.8em;
    color: #2c3e50;
    margin-bottom: 15px;
}

.preview-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
}

.preview-actions {
    margin-top: auto;
}

.download-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 20px;
}

.download-price {
    font-size: 1.3em;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.download-info small {
    opacity: 0.9;
    font-size: 0.9em;
}

.btn-download {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-download i {
    font-size: 1.2em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .preview-container {
        flex-direction: column;
        gap: 20px;
        padding: 15px;
    }

    .preview-image-container {
        flex: none;
        height: 50vh;
    }

    .preview-info {
        flex: none;
    }

    .preview-content {
        width: 98%;
        max-height: 95vh;
    }
}

/* 图片懒加载效果 */
.gallery-item img {
    transition: opacity 0.3s ease;
}

.gallery-item img[data-loaded="false"] {
    opacity: 0;
}

.gallery-item img[data-loaded="true"] {
    opacity: 1;
}
