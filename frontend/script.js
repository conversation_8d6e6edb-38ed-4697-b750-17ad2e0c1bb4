// 摄影作品数据 - 将从后端API动态加载
let photographyData = [];

// 全局变量
let currentFilter = 'all';
let currentPhotoData = null;
let paymentPollingInterval = null;

// DOM 元素
const galleryGrid = document.getElementById('gallery-grid');
const tabButtons = document.querySelectorAll('.tab-button');
const paymentModal = document.getElementById('payment-modal');
const previewModal = document.getElementById('preview-modal');
const successModal = document.getElementById('success-modal');
const userEmailInput = document.getElementById('user-email');
const confirmedEmailSpan = document.getElementById('confirmed-email');
const photoPriceSpan = document.getElementById('photo-price');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    loadPhotographyData();
    initializeModalEvents();
});

// 从后端API加载摄影作品数据
async function loadPhotographyData() {
    try {
        // 显示加载状态
        showLoadingState();

        const apiUrl = window.APP_CONFIG?.API_BASE_URL
            ? `${window.APP_CONFIG.API_BASE_URL}/api/photography-data`
            : '/api/photography-data';
        const response = await fetch(apiUrl);
        const result = await response.json();

        if (result.success) {
            photographyData = result.data;
            console.log(`成功加载 ${result.count} 张摄影作品`);
            renderGallery();
        } else {
            console.error('加载摄影作品数据失败:', result.message);
            showErrorState('加载作品数据失败，请刷新页面重试');
        }
    } catch (error) {
        console.error('网络错误:', error);
        showErrorState('网络连接失败，请检查网络后刷新页面');
    }
}

// 显示加载状态
function showLoadingState() {
    const galleryGrid = document.getElementById('gallery-grid');
    galleryGrid.innerHTML = `
        <div class="loading-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
            <div class="loading-spinner" style="
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            "></div>
            <p style="color: #666; font-size: 16px;">正在加载摄影作品...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
}

// 显示错误状态
function showErrorState(message) {
    const galleryGrid = document.getElementById('gallery-grid');
    galleryGrid.innerHTML = `
        <div class="error-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
            <div style="font-size: 48px; color: #dc3545; margin-bottom: 20px;">⚠️</div>
            <h3 style="color: #dc3545; margin-bottom: 10px;">加载失败</h3>
            <p style="color: #666; margin-bottom: 20px;">${message}</p>
            <button onclick="loadPhotographyData()" style="
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            ">重新加载</button>
        </div>
    `;
}

// 导航栏功能
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                // 关闭移动端菜单
                if (navMenu) {
                    navMenu.classList.remove('active');
                }
            }
        });
    });
}

// 分类标签切换
tabButtons.forEach(button => {
    button.addEventListener('click', function() {
        // 移除所有活动状态
        tabButtons.forEach(btn => btn.classList.remove('active'));
        // 添加当前按钮的活动状态
        this.classList.add('active');
        
        // 获取分类
        currentFilter = this.getAttribute('data-category');
        
        // 重新渲染画廊
        renderGallery();
    });
});

// 渲染作品画廊
function renderGallery() {
    // 检查数据是否已加载
    if (!photographyData || photographyData.length === 0) {
        return;
    }

    const filteredData = currentFilter === 'all'
        ? photographyData
        : photographyData.filter(item => item.category === currentFilter);

    galleryGrid.innerHTML = '';

    // 如果没有符合条件的作品
    if (filteredData.length === 0) {
        galleryGrid.innerHTML = `
            <div class="no-data-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                <div style="font-size: 48px; color: #ccc; margin-bottom: 20px;">📷</div>
                <h3 style="color: #666; margin-bottom: 10px;">暂无作品</h3>
                <p style="color: #999;">该分类下暂时没有摄影作品</p>
            </div>
        `;
        return;
    }

    filteredData.forEach(item => {
        const galleryItem = createGalleryItem(item);
        galleryGrid.appendChild(galleryItem);
    });

    // 添加加载动画
    galleryGrid.querySelectorAll('.gallery-item').forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('loading');
        setTimeout(() => {
            item.classList.add('loaded');
        }, 100 + index * 100);
    });
}

// 创建作品项目元素
function createGalleryItem(data) {
    const div = document.createElement('div');
    div.className = 'gallery-item';
    div.setAttribute('data-category', data.category);
    
    const categoryNames = {
        landscape: '风景',
        portrait: '人物',
        food: '食物',
        animal: '动物',
        architecture: '建筑',
        realistic: '写实'
    };
    
    div.innerHTML = `
        <img src="${data.image}" alt="${data.title}" data-loaded="false">
        <div class="gallery-item-content">
            <h3>${data.title}</h3>
            <p>${data.description}</p>
            <div class="gallery-item-footer">
                <span class="category-badge">${categoryNames[data.category]}</span>
                <span class="preview-tag">点击预览</span>
            </div>
        </div>
    `;
    
    // 图片加载事件
    const img = div.querySelector('img');
    img.onload = function() {
        this.setAttribute('data-loaded', 'true');
    };
    
    // 点击事件 - 统一打开预览模态框
    div.addEventListener('click', function() {
        openPreviewModal(data);
    });
    
    return div;
}

// 打开图片预览模态框
function openPreviewModal(data) {
    currentPhotoData = data;

    // 设置预览图片和信息
    const previewImage = document.getElementById('preview-image');
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');

    previewImage.src = data.image;
    previewImage.alt = data.title;
    previewTitle.textContent = data.title;
    previewDescription.textContent = data.description;

    // 显示预览模态框
    previewModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭图片预览模态框
function closePreviewModal() {
    previewModal.style.display = 'none';
    document.body.style.overflow = 'auto';
    currentPhotoData = null;
}

// 开始下载付费流程
function startDownloadPayment() {
    if (!currentPhotoData) {
        alert('请先选择要下载的作品');
        return;
    }

    // 关闭预览模态框
    closePreviewModal();

    // 打开付费模态框，价格固定为2元
    const downloadData = {
        ...currentPhotoData,
        price: 2,
        isPaid: true
    };
    openPaymentModal(downloadData);
}

// 打开付费弹窗
function openPaymentModal(data) {
    currentPhotoData = data;
    photoPriceSpan.textContent = data.price;
    userEmailInput.value = '';
    paymentModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 重置二维码为默认状态
    const qrImage = document.getElementById('qr-image');
    const defaultSvg = `
        <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <text x="100" y="90" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">请先输入邮箱地址</text>
            <text x="100" y="110" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">然后点击"我已支付"生成二维码</text>
        </svg>
    `;
    qrImage.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(defaultSvg);
}

// 显示二维码加载状态
function showQRCodeLoading() {
    const qrImage = document.getElementById('qr-image');
    const loadingSvg = `
        <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
            <circle cx="100" cy="80" r="20" fill="none" stroke="#007bff" stroke-width="3" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
            </circle>
            <text x="100" y="130" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">正在生成二维码...</text>
            <text x="100" y="150" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">请稍候</text>
        </svg>
    `;
    qrImage.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(loadingSvg);
}

// 显示支付错误信息
function showPaymentError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'payment-error-toast';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-size: 14px;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                document.body.removeChild(errorDiv);
            }, 300);
        }
    }, 3000);
    
    // 添加动画样式
    if (!document.getElementById('toast-animations')) {
        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// 生成支付宝二维码
async function generatePaymentQR(data) {
    const qrImage = document.getElementById('qr-image');
    const userEmail = document.getElementById('user-email').value.trim();
    
    if (!userEmail || !isValidEmail(userEmail)) {
        showPaymentError('请先输入有效的邮箱地址');
        return;
    }
    
    try {
        // 设置超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), PAYMENT_CONFIG.CREATE_ORDER_TIMEOUT);
        
        // 调用后端API创建支付订单
        const apiUrl = window.APP_CONFIG?.API_BASE_URL
            ? `${window.APP_CONFIG.API_BASE_URL}/api/create-payment`
            : '/api/create-payment';
            
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                photoId: data.id || Date.now(),
                price: data.price,
                userEmail: userEmail,
                photoTitle: data.title
            }),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            // 检查二维码数据格式
            if (result.qrCode) {
                // 显示二维码
                displayQRCode(result.qrCode);
                
                // 保存订单号，用于查询支付状态
                currentPhotoData.orderNo = result.orderNo;
                
                // 显示支付提示
                updatePaymentInstructions('请使用支付宝扫描二维码完成支付');
                
                // 开始轮询支付状态
                startPaymentStatusPolling(result.orderNo);
                
                // 设置二维码过期提醒
                setQRCodeExpireTimer();
                
            } else {
                throw new Error('支付二维码生成失败');
            }
        } else {
            throw new Error(result.message || '创建支付订单失败');
        }
    } catch (error) {
        console.error('生成支付二维码失败:', error);
        
        if (error.name === 'AbortError') {
            showQRCodeError('请求超时，请检查网络连接后重试');
        } else {
            showQRCodeError(error.message || '网络错误，请重试');
        }
    }
}

// 显示二维码
function displayQRCode(qrCodeData) {
    const qrImage = document.getElementById('qr-image');
    
    if (qrCodeData.startsWith('http')) {
        // 如果是URL格式，使用第三方二维码生成服务
        qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeData)}`;
        qrImage.onerror = () => {
            // 备用方案：使用本地二维码生成
            showQRCodeError('二维码加载失败，请刷新重试');
        };
    } else {
        // 如果是base64格式，直接使用
        qrImage.src = qrCodeData.startsWith('data:') ? qrCodeData : `data:image/png;base64,${qrCodeData}`;
    }
}

// 显示二维码错误状态
function showQRCodeError(message) {
    const qrImage = document.getElementById('qr-image');
    const errorSvg = `
        <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#fff2f4" stroke="#f5c6cb" stroke-width="2"/>
            <circle cx="100" cy="70" r="25" fill="#dc3545"/>
            <text x="100" y="78" text-anchor="middle" font-family="Arial" font-size="20" fill="white">!</text>
            <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="12" fill="#721c24">生成失败</text>
            <text x="100" y="140" text-anchor="middle" font-family="Arial" font-size="10" fill="#721c24">${message}</text>
            <text x="100" y="170" text-anchor="middle" font-family="Arial" font-size="10" fill="#6c757d" style="cursor: pointer;" onclick="confirmPayment()">点击重试</text>
        </svg>
    `;
    qrImage.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(errorSvg);
    
    // 同时显示错误提示
    showPaymentError(message);
}

// 更新支付说明
function updatePaymentInstructions(message) {
    const tipsElement = document.querySelector('.payment-tips');
    if (tipsElement) {
        tipsElement.textContent = message;
    }
}

// 设置二维码过期提醒
function setQRCodeExpireTimer() {
    setTimeout(() => {
        if (currentPhotoData && !paymentPollingInterval) {
            updatePaymentInstructions('二维码已过期，请重新生成');
            showQRCodeError('二维码已过期，请重新生成');
        }
    }, PAYMENT_CONFIG.QR_CODE_TIMEOUT);
}

// 轮询支付状态
function startPaymentStatusPolling(orderNo) {
    // 清除之前的轮询
    if (paymentPollingInterval) {
        clearInterval(paymentPollingInterval);
    }
    
    let attempts = 0;
    let consecutiveErrors = 0;
    
    paymentPollingInterval = setInterval(async () => {
        attempts++;
        
        // 超过最大尝试次数则停止
        if (attempts > PAYMENT_CONFIG.MAX_POLLING_ATTEMPTS) {
            clearInterval(paymentPollingInterval);
            paymentPollingInterval = null;
            showPaymentTimeout();
            return;
        }
        
        try {
            // 设置请求超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), PAYMENT_CONFIG.CHECK_STATUS_TIMEOUT);
            
            const apiUrl = window.APP_CONFIG?.API_BASE_URL
                ? `${window.APP_CONFIG.API_BASE_URL}/api/payment-status/${orderNo}`
                : `/api/payment-status/${orderNo}`;
                
            const response = await fetch(apiUrl, {
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            // 重置连续错误计数
            consecutiveErrors = 0;
            
            if (result.success) {
                if (result.status === 'TRADE_SUCCESS' || result.status === 'TRADE_FINISHED') {
                    // 支付成功
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                    const userEmail = document.getElementById('user-email').value;
                    paymentSuccess(userEmail, result.tradeNo);
                } else if (result.status === 'TRADE_CLOSED') {
                    // 支付已关闭
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                    showPaymentClosed();
                } else {
                    // 更新轮询状态显示
                    updatePollingStatus(attempts);
                }
            } else {
                console.warn('查询支付状态返回错误:', result.error);
                consecutiveErrors++;
                
                // 如果连续错误过多，停止轮询
                if (consecutiveErrors >= 3) {
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                    showPaymentQueryError();
                }
            }
            
        } catch (error) {
            console.error('查询支付状态失败:', error);
            consecutiveErrors++;
            
            // 如果连续错误过多，停止轮询
            if (consecutiveErrors >= 3) {
                clearInterval(paymentPollingInterval);
                paymentPollingInterval = null;
                
                if (error.name === 'AbortError') {
                    showPaymentQueryError('网络请求超时，请检查网络连接');
                } else {
                    showPaymentQueryError('查询支付状态失败，请手动刷新页面确认');
                }
            }
        }
    }, PAYMENT_CONFIG.POLL_INTERVAL);
    
    // 设置总体超时（10分钟）
    setTimeout(() => {
        if (paymentPollingInterval) {
            clearInterval(paymentPollingInterval);
            paymentPollingInterval = null;
            showPaymentTimeout();
        }
    }, PAYMENT_CONFIG.QR_CODE_TIMEOUT);
}

// 更新轮询状态显示
function updatePollingStatus(attempts) {
    const remainingAttempts = PAYMENT_CONFIG.MAX_POLLING_ATTEMPTS - attempts;
    const minutes = Math.floor(remainingAttempts * PAYMENT_CONFIG.POLL_INTERVAL / 60000);
    const seconds = Math.floor((remainingAttempts * PAYMENT_CONFIG.POLL_INTERVAL % 60000) / 1000);
    
    updatePaymentInstructions(`等待支付确认... (${minutes}:${seconds.toString().padStart(2, '0')})`);
}

// 显示支付超时
function showPaymentTimeout() {
    updatePaymentInstructions('支付超时，请重新生成二维码');
    showQRCodeError('支付已超时，请重新生成二维码');
    
    // 添加重新支付按钮
    addRetryPaymentButton();
}

// 显示支付关闭
function showPaymentClosed() {
    updatePaymentInstructions('支付已关闭');
    showPaymentError('支付已关闭，请重新开始支付流程');
    closePaymentModal();
}

// 显示支付查询错误
function showPaymentQueryError(message = '查询支付状态失败') {
    updatePaymentInstructions(message);
    showPaymentError(message);
    
    // 添加手动查询按钮
    addManualCheckButton();
}

// 添加重试支付按钮
function addRetryPaymentButton() {
    const paymentActions = document.querySelector('.payment-actions');
    if (paymentActions && !paymentActions.querySelector('.btn-retry')) {
        const retryBtn = document.createElement('button');
        retryBtn.className = 'btn-secondary btn-retry';
        retryBtn.textContent = '重新支付';
        retryBtn.onclick = () => {
            paymentActions.removeChild(retryBtn);
            confirmPayment();
        };
        paymentActions.appendChild(retryBtn);
    }
}

// 添加手动查询按钮
function addManualCheckButton() {
    const paymentActions = document.querySelector('.payment-actions');
    if (paymentActions && !paymentActions.querySelector('.btn-check')) {
        const checkBtn = document.createElement('button');
        checkBtn.className = 'btn-secondary btn-check';
        checkBtn.textContent = '手动查询';
        checkBtn.onclick = () => {
            if (currentPhotoData && currentPhotoData.orderNo) {
                startPaymentStatusPolling(currentPhotoData.orderNo);
                paymentActions.removeChild(checkBtn);
            }
        };
        paymentActions.appendChild(checkBtn);
    }
}

// 支付配置常量
const PAYMENT_CONFIG = {
    MAX_POLLING_ATTEMPTS: 20,     // 最大轮询次数
    POLL_INTERVAL: 3000,          // 轮询间隔(毫秒)
    CREATE_ORDER_TIMEOUT: 30000,  // 创建订单超时时间
    CHECK_STATUS_TIMEOUT: 10000,  // 检查状态超时时间
    QR_CODE_TIMEOUT: 600000       // 二维码10分钟超时
};

// 支付状态枚举
const PAYMENT_STATUS = {
    PENDING: 'pending',
    SUCCESS: 'success',
    FAILED: 'failed',
    EXPIRED: 'expired',
    ERROR: 'error'
};

// 修改确认支付函数
function confirmPayment() {
    const email = userEmailInput.value.trim();
    const confirmBtn = document.querySelector('.btn-primary');
    
    // 防止重复点击
    if (confirmBtn.disabled) {
        return;
    }
    
    if (!email) {
        showPaymentError('请输入邮箱地址');
        userEmailInput.focus();
        return;
    }
    
    if (!isValidEmail(email)) {
        showPaymentError('请输入有效的邮箱地址');
        userEmailInput.focus();
        return;
    }
    
    // 禁用按钮防止重复点击
    confirmBtn.disabled = true;
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '生成中...';
    
    // 显示生成二维码的加载状态
    showQRCodeLoading();
    
    // 生成支付二维码
    generatePaymentQR(currentPhotoData).finally(() => {
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.textContent = originalText;
    });
}

// 关闭付费弹窗
function closePaymentModal() {
    paymentModal.style.display = 'none';
    document.body.style.overflow = '';
    currentPhotoData = null;
    userEmailInput.value = '';
    
    // 清理支付状态轮询
    if (paymentPollingInterval) {
        clearInterval(paymentPollingInterval);
        paymentPollingInterval = null;
    }
}



// 显示支付处理中
function showPaymentProcessing() {
    const confirmBtn = document.querySelector('.btn-primary');
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '验证支付中...';
    confirmBtn.disabled = true;
    
    setTimeout(() => {
        confirmBtn.textContent = originalText;
        confirmBtn.disabled = false;
    }, 2000);
}

// 支付成功
function paymentSuccess(email, tradeNo) {
    closePaymentModal();
    confirmedEmailSpan.textContent = email;
    
    // 显示交易号（如果有）
    if (tradeNo) {
        const tradeNoElement = document.getElementById('trade-no');
        const tradeDisplayElement = document.getElementById('trade-display');
        if (tradeNoElement && tradeDisplayElement) {
            tradeNoElement.textContent = tradeNo;
            tradeDisplayElement.style.display = 'block';
        }
    }
    
    successModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 记录支付成功事件
    console.log('支付成功', {
        email,
        tradeNo,
        orderNo: currentPhotoData?.orderNo,
        photoTitle: currentPhotoData?.title,
        timestamp: new Date().toISOString()
    });
    
    // 发送成功事件到后端（用于分析）
    fetch('/api/log-payment-event', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            eventType: 'FRONTEND_PAYMENT_SUCCESS',
            orderNo: currentPhotoData?.orderNo,
            email: email,
            tradeNo: tradeNo,
            timestamp: new Date().toISOString()
        })
    }).catch(error => {
        console.warn('发送成功事件失败:', error);
    });
}

// 模拟发送邮件
function sendPhotoToEmail(email, photoData) {
    console.log(`发送高清作品到邮箱: ${email}`);
    console.log(`作品信息:`, photoData);
}

// 关闭成功弹窗
function closeSuccessModal() {
    successModal.style.display = 'none';
    document.body.style.overflow = '';
}

// 邮箱验证
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 初始化弹窗事件
function initializeModalEvents() {
    // 关闭按钮事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
            document.body.style.overflow = '';
        }
    });
    
    // ESC 键关闭弹窗
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const visibleModal = document.querySelector('.modal[style*="block"]');
            if (visibleModal) {
                visibleModal.style.display = 'none';
                document.body.style.overflow = '';
            }
        }
    });
}

// 滚动效果
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    }
});